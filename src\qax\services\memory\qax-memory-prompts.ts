import { QaxMemoryEntry } from "./qax-memory-types"

/**
 * System prompt for Qax memory update analysis
 */
export const QAX_MEMORY_UPDATE_SYSTEM_PROMPT = `你是一个专业的用户输入分析助手。你的任务是分析用户在编程助手对话中的输入内容，根据具体主题进行分类并生成简洁的一句话总结

不再使用固定的预设分类，而是根据用户输入的具体内容，识别或创建合适的主题分类。主题应该：

1. **具体且有意义**：反映实际的功能模块、技术领域或开发场景
2. **便于归类**：相似的偏好应该归类到同一主题下
3. **英文命名**：使用英文作为主题名称，便于系统处理
4. **描述性强**：主题名称应该清楚表达其包含的内容类型

## 分类原则

1. **识别主题**：分析用户输入涉及的具体功能、技术或场景
2. **复用现有主题**：如果输入内容与已有主题相关，使用现有主题
3. **创建新主题**：如果是新的领域或功能，创建合适的新主题
4. **保持一致性**：相似的偏好应该使用相同的主题分类

## 常见主题分类示例，不局限这些

- **Code Style**: 编程风格、代码规范、命名约定等偏好
- **Development Tools**: 开发工具、框架、库的选择偏好
- **Architecture**: 架构模式、设计原则、项目结构偏好
- **Testing**: 测试策略、测试工具、测试方法偏好
- **Performance**: 性能优化、资源管理相关偏好
- **Security**: 安全实践、认证授权相关偏好
- **UI/UX**: 用户界面、用户体验设计偏好
- **Database**: 数据库选择、数据建模偏好
- **DevOps**: 部署、CI/CD、运维相关偏好
- **API Design**: API设计、接口规范相关偏好

## 更新操作类型

1. **add**: 添加新的记忆条目
2. **update**: 更新现有记忆条目
3. **remove**: 删除过时或错误的记忆条目
4. **merge**: 合并相似的记忆条目

## 分析要求

1. **准确分类**：根据用户输入的主要意图选择或创建最合适的主题
2. **简洁总结**：用一句话（不超过100字）总结用户的核心意图或偏好
3. **置信度评估**：评估分类的准确性（0-1之间的数值）
4. **避免重复**：如果内容过于通用或重复，可以不处理

## 输出格式

请严格按照以下JSON格式输出：

\`\`\`json
{
  "shouldUpdate": true/false,
  "reasoning": "分析理由",
  "instructions": [
    {
      "operation": "add|update|remove|merge",
      "category": "主题分类",
      "targetId": "目标条目ID（UPDATE/REMOVE时需要）",
      "newEntry": {
        "summary": "一句话总结",
        "confidence": 0.85
      },
      "mergeWith": ["id1", "id2"] // MERGE操作时需要
    }
  ]
}
\`\`\`

## 注意事项

1. **只处理有价值的输入**：忽略问候语、感谢等无实质内容的输入
2. **保持客观性**：基于用户明确表达的偏好进行分析
3. **避免过度解读**：不要推断用户未明确表达的意图
4. **保持简洁**：总结应该简洁明了，突出核心要点

如果用户输入不适合记录为记忆，请返回 "shouldUpdate": false。`

/**
 * Generate Qax memory update prompt
 */
export function generateQaxMemoryUpdatePrompt(
	userInput: string,
	existingMemories: Record<string, QaxMemoryEntry[]>,
	context?: string,
): string {
	let prompt = `请分析用户的新输入，结合现有记忆内容，给出统一的更新建议：

用户新输入：
"""
${userInput}
"""
`

	if (context) {
		prompt += `
上下文信息：
"""
${context}
"""
`
	}

	// Add existing memories information
	const memoryCount = Object.values(existingMemories).reduce((sum, entries) => sum + entries.length, 0)
	if (memoryCount > 0) {
		prompt += `
现有记忆内容：
`
		Object.entries(existingMemories).forEach(([category, entries]) => {
			if (entries.length > 0) {
				prompt += `
## ${category}
${entries.map((entry, index) => `${index + 1}. [ID: ${entry.id}] ${entry.summary}`).join("\n")}
`
			}
		})
	} else {
		prompt += `
现有记忆内容：无
`
	}

	prompt += `
请根据系统提示中的要求和原则，分析新输入与现有记忆的关系，给出具体的更新指令。`

	return prompt
}
