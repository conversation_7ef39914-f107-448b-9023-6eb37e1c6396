// Autocomplete Prompt Debugger - 用于手动调试和优化Prompt (修复版本)
// 支持 generateCompletion 和 generateFimCompletion 两种模式
//
// 🎯 使用方法:
// 1. 修改 mockDocumentContent 变量来测试不同的代码内容
// 2. 修改 testCases 数组来测试不同的光标位置
// 3. 运行 node test-autocomplete-prompt-debugger-fixed.js
// 4. 观察生成的Prompt来优化模板和上下文提取
//
// 📊 输出内容:
// - 标准API模式: 显示系统提示和用户提示的完整内容
// - FIM模式: 显示prefix和suffix的内容和长度统计
// - 两种模式的模拟API响应
//
// 🔧 调试技巧:
// - 比较两种模式的Prompt差异
// - 检查上下文提取是否合理
// - 验证{{FILL_HERE}}占位符位置是否正确
// - 观察不同位置的补全效果
//
// 🔑 API配置:
// 设置环境变量或修改代码中的API_CONFIG:
// - OPENAI_API_KEY: OpenAI API密钥
// - OPENAI_BASE_URL: OpenAI API基础URL (可选)
// - OPENAI_MODEL_ID: 使用的模型ID (默认: gpt-4o-mini)
// - FIM_API_KEY: FIM API密钥
// - FIM_BASE_URL: FIM API基础URL (默认: DeepSeek API)

process.env.TS_NODE_PROJECT = "tsconfig.test.json"

require("ts-node").register({
	transpileOnly: true,
	project: "tsconfig.test.json",
	compilerOptions: {
		module: "commonjs",
		moduleResolution: "node",
		target: "es2020",
		esModuleInterop: true,
		allowSyntheticDefaultImports: true,
		skipLibCheck: true,
	},
})

// Mock vscode module
const vscode = {
	Position: class Position {
		constructor(line, character) {
			this.line = line
			this.character = character
		}
	},
	Range: class Range {
		constructor(start, end) {
			this.start = start
			this.end = end
		}
	},
	Uri: {
		file: (path) => ({ fsPath: path }),
	},
	EndOfLine: {
		LF: 1,
	},
	window: {
		activeTextEditor: null,
	},
}

const Module = require("module")
const originalRequire = Module.prototype.require

Module.prototype.require = function (id) {
	if (id === "vscode") {
		return vscode
	}
	return originalRequire.apply(this, arguments)
}

// Mock document content - 使用提供的JavaScript代码
const mockDocumentContent = `// 应用初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 设置moment中文
    moment.locale('zh-cn');
    
    // 初始化API桥接
    if (window.require) {
        // Electron环境
        const { ipcRenderer } = window.require('electron');
        
        window.api = {
            getEvents: (params) => ipcRenderer.invoke('get-events', params),
            getEventsByDate: (date) => ipcRenderer.invoke('get-events-by-date', date),
            getEventsByWeek: (date) => ipcRenderer.invoke('get-events-by-week', date),
            getEventsByMonth: (date) => ipcRenderer.invoke('get-events-by-month', date),
            addEvent: (eventData) => ipcRenderer.invoke('add-event', eventData),
            updateEvent: (params) => ipcRenderer.invoke('update-event', params),
            deleteEvent: (id) => ipcRenderer.invoke('delete-event', id),
            getTags: () => ipcRenderer.invoke('get-tags'),
            
            onMenuChangeView: (callback) => ipcRenderer.on('menu-change-view', callback),
            onMenuNewEvent: (callback) => ipcRenderer.on('menu-new-event', callback),
            onMenuRefresh: (callback) => ipcRenderer.invoke('menu-refresh', callback),
            onMenuAbout: (callback) => ipcRenderer.on('menu-about', callback),
            
            // 添加ping方法用于健康检查
            ping: () => ipcRenderer.invoke('ping')
        };
    } else {
        // 浏览器环境（开发测试）
        window.api = {
            getEvents: async () => [],
            getEventsByDate: async () => [],
            getEventsByWeek: async () => [],
            getEventsByMonth: async () => [],
            addEvent: async () => ({ success: true, id: 1 }),
            updateEvent: async () => ({ success: true, changes: 1 }),
            deleteEvent: async () => ({ success: true, changes: 1 }),
            getTags: async () => [
                { id: 1, name: '工作', color: '#2196F3' },
                { id: 2, name: '个人', color: '#FF9800' },
                { id: 3, name: '会议', color: '#4CAF50' },
                { id: 4, name: '重要', color: '#F44336' },
                { id: 5, name: '提醒', color: '#9C27B0' }
            ],
            
            onMenuChangeView: () => {},
            onMenuNewEvent: () => {},
            onMenuRefresh: () => {},
            onMenuAbout: () => {},
            
            // ping方法用于健康检查
            ping: async () => true
        };
    }

    // 初始化应用
    console.log('跨平台日程表应用已启动');
    
    // 检查渲染进程是否响应
	let isRendererResponsive = true;
    
    // 设置渲染进程健康检查
    const checkRendererHealth = () => {
        if (!isRendererResponsive) {
            return;
        }
        
        const timeoutId = setTimeout(() => {
            isRendererResponsive = false;
            console.error('检测到渲染进程无响应');
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.send('renderer-unresponsive');
            }
        }, 30000);
        
        try {
            // 发送健康检查请求
            if (window.require) {
                const { ipcRenderer } = window.require('electron');
                ipcRenderer.invoke('ping').finally(() => {
                    clearTimeout(timeoutId);
                    isRendererResponsive = true;
                });
            } else {
                clearTimeout(timeoutId);
                isRendererResponsive = true;
            }
        } catch (error) {
            clearTimeout(timeoutId);
            isRendererResponsive = false;
            console.error('渲染进程健康检查失败:', error);
        }
    };
    
    // 每5秒检查一次渲染进程状态
    setInterval(checkRendererHealth, 5000);
    checkRendererHealth();
    
    // 检查日志是否正常输出
    const checkConsoleOutput = () => {
        if (typeof console._log === 'undefined') {
            // 保存原始console方法
            console._log = console.log;
            console._error = console.error;
            console._warn = console.warn;
        }
        
        // 使用标准编码输出日志
        const testLog = '测试日志输出';
        console._log('日志测试:', testLog);
        console._error('错误测试:', testLog);
        console._warn('警告测试:', testLog);
    };
    
    // 定期检查console输出
    setInterval(checkConsoleOutput, 10000); // 每10秒检查一次
    checkConsoleOutput();
    
    // 初始化AI日程助手按钮事件
    document.getElementById('btn-ai-schedule').addEventListener('click', () => {
        // 创建AI日程对话框内容
        const aiModalContent = \`
            <div class="ai-modal-content">
                <div class="modal-header">
                    <h3>AI日程助手</h3>
                    <button class="close-btn" id="close-ai-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>描述您的日程需求</label>
                        <textarea id="ai-schedule-input" rows="5" placeholder="例如：每周一上午10点开团队会议，持续1小时"></textarea>
                    </div>
                    <div class="form-group">
                        <label>建议的日程安排</label>
                        <div id="ai-schedule-output" class="schedule-output">
                            <p>AI将为您分析并生成日程建议...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="btn-ai-close">关闭</button>
                    <button type="button" class="btn btn-primary" id="btn-ai-add-schedule">添加日程</button>
                </div>
            </div>
        \`;
        
        // 确保元素加载完成
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            if (document.getElementById('btn-ai-schedule') && document.body) {
                clearInterval(checkInterval);
                
                // 创建模态框
                const aiModal = document.createElement('div');
                aiModal.className = 'modal ai-modal';
                aiModal.innerHTML = aiModalContent;
                document.body.appendChild(aiModal);
                
                // 初始化模态框事件
                initModalEvents(aiModal);
            } else if (checkCount++ > 100) { // 超时检测
                clearInterval(checkInterval);
                console.error('创建AI日程对话框超时');
            }
        }, 100);
    });
});


function initModalEvents(aiModal) {
    // 关闭按钮事件
    aiModal.querySelector('#close-ai-modal').addEventListener('click', () => {
        aiModal.remove();
    });
    
    aiModal.querySelector('#btn-ai-close').addEventListener('click', () => {
        aiModal.remove();
    });
    
    // AI分析按钮事件
    const analyzeButton = aiModal.querySelector('#btn-ai-add-schedule');
    if (analyzeButton) {
        analyzeButton.addEventListener('click', async () => {
            const input = aiModal.querySelector('#ai-schedule-input').value.trim();
            if (!input) {
                alert('请输入日程描述');
                return;
            }
            
            try {
                // 显示加载状态
                const outputDiv = aiModal.querySelector('#ai-schedule-output');
                outputDiv.innerHTML = '<p>正在分析您的日程需求...</p>';
                
                // 调用AI服务解析日程
                const schedule = await window.aiService.parseEvent(input);
                
                // 显示解析结果
                outputDiv.innerHTML = \`
                    <div class="schedule-result">
                        <h4>解析结果</h4>
                        <p><strong>标题:</strong> \${schedule.title}</p>
                        <p><strong>时间:</strong> \${schedule.start_time.toLocaleString()} - \${schedule.end_time.toLocaleString()}</p>
                        <p><strong>分类:</strong> \${schedule.category}</p>
                        \${schedule.description ? \`<p><strong>描述:</strong> \${schedule.description}</p>\` : ''}
                        \${schedule.location ? \`<p><strong>地点:</strong> \${schedule.location}</p>\` : ''}
                    </div>
                \`;
                
                // 创建日程对象
                const event = {
                    title: schedule.title,
                    description: schedule.description || '',
                    start_time: schedule.start_time.toISOString(),
                    end_time: schedule.end_time.toISOString(),
                    location: schedule.location || '',
                    category: schedule.category,
                    color_bg: '#E3F2FD',
                    color_fg: '#1976D2',
                    is_all_day: schedule.is_all_day || false
                };
                
                // 添加日程到数据库
                const result = await window.api.addEvent(event);
                
                if (result.success) {
                    alert('日程添加成功');
                    aiModal.remove();
                    // 刷新日历视图
                    window.dispatchEvent(new Event('refresh-calendar'));
                } else {
                    alert('日程添加失败');
                }
                
                // 阻止事件冒泡
                return false;
            } catch (error) {
                console.error('AI日程解析错误:', error);
                aiModal.querySelector('#ai-schedule-output').innerHTML = \`
                    <div class="error">
                        <p>解析失败: \${error.message}</p>
                        <p>请尝试更详细或更具体的描述</p>
                    </div>
                \`;
            }
        });
    }
}

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('全局错误:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});`

// Mock TextDocument
class MockTextDocument {
	constructor(content, languageId = "javascript", fileName = "app.js") {
		this._lines = content.split("\n")
		this._languageId = languageId
		this._content = content
		this._fileName = fileName
		this._uri = vscode.Uri.file(fileName)
	}

	get lineCount() {
		return this._lines.length
	}
	get languageId() {
		return this._languageId
	}
	get fileName() {
		return this._fileName
	}
	get uri() {
		return this._uri
	}

	lineAt(line) {
		return {
			text: this._lines[line] || "",
			lineNumber: line,
		}
	}

	getText(range) {
		if (!range) {
			// 如果没有提供range，返回整个文档内容
			return this._content
		}

		// 如果提供了range，返回指定范围的内容
		const startLine = range.start.line
		const startChar = range.start.character
		const endLine = range.end.line
		const endChar = range.end.character

		if (startLine === endLine) {
			// 同一行内的范围
			return this._lines[startLine].substring(startChar, endChar)
		} else {
			// 跨行的范围
			const lines = []

			// 第一行：从startChar到行尾
			lines.push(this._lines[startLine].substring(startChar))

			// 中间的完整行
			for (let i = startLine + 1; i < endLine; i++) {
				lines.push(this._lines[i])
			}

			// 最后一行：从行首到endChar
			if (endLine < this._lines.length) {
				lines.push(this._lines[endLine].substring(0, endChar))
			}

			return lines.join("\n")
		}
	}
}

console.log("🚀 Autocomplete Prompt Debugger (修复版本)")
console.log("=".repeat(80))
console.log("📋 功能说明:")
console.log("- 🔧 测试和调试 generateCompletion (标准API模式)")
console.log("- 🔧 测试和调试 generateFimCompletion (FIM模式)")
console.log("- 📝 使用真实的文档内容和模板")
console.log("- 🎯 手动优化和调试Prompt")
console.log("=".repeat(80) + "\n")

// 测试 MockTextDocument 的 getText(range) 方法
function testMockDocument() {
	console.log("🧪 测试 MockTextDocument.getText(range) 方法")
	console.log("─".repeat(60))

	const testDoc = new MockTextDocument(
		`line 0
line 1
line 2
line 3`,
		"javascript",
	)

	// 测试无参数调用
	console.log("📝 测试1: getText() - 整个文档")
	console.log(`结果: "${testDoc.getText()}"`)

	// 测试同一行范围
	console.log("\n📝 测试2: getText(range) - 同一行范围")
	const sameLineRange = new vscode.Range(new vscode.Position(1, 2), new vscode.Position(1, 5))
	console.log(`范围: 第2行第3列到第6列`)
	console.log(`结果: "${testDoc.getText(sameLineRange)}"`)

	// 测试跨行范围
	console.log("\n📝 测试3: getText(range) - 跨行范围")
	const multiLineRange = new vscode.Range(new vscode.Position(1, 2), new vscode.Position(3, 2))
	console.log(`范围: 第2行第3列到第4行第3列`)
	console.log(`结果: "${testDoc.getText(multiLineRange)}"`)

	console.log("\n✅ MockTextDocument 测试完成\n")
}

// 运行mock document测试
//testMockDocument()

// 测试场景配置
const testCases = [
	{
		name: "🎯 事件监听器",
		position: new vscode.Position(128, 22), // 在事件监听器部分
		description: "测试在事件监听器中的补全，应该补全事件处理代码",
	},
	{
		name: "test1",
		position: new vscode.Position(60, 4	), // 在事件监听器部分
		description: "",
	},
	{
		name: "test2",
		position: new vscode.Position(170, 0	), // 在事件监听器部分
		description: "",
	},
]

// 配置真实的API接口
const API_CONFIG = {
	// OpenAI API 配置 (用于标准API模式)
	openai: {
		apiKey: process.env.OPENAI_API_KEY || "33147eaa9875abac5f7fd5a1aa830d0bfd486c8b",
		baseUrl: process.env.OPENAI_BASE_URL || "https://aip.b.qianxin-inc.cn/v2",
		modelId: process.env.OPENAI_MODEL_ID || "Qwen3-Coder-30B-A3B-Instruct",
		//modelId: process.env.OPENAI_MODEL_ID || "Qwen3-Coder-480B-A35B-Instruct",
		temperature: 0.7,
		maxTokens: 1000,
	},

	// FIM API 配置 (用于FIM模式)
	fim: {
		apiKey: process.env.FIM_API_KEY || "sk-b106452f497642b5a7921f26ecf8c2a6",
		baseUrl: process.env.FIM_BASE_URL || "https://dashscope.aliyuncs.com/compatible-mode/v1",
		temperature: 0.1,
		maxTokens: 500,
		requestTimeoutMs: 30000,
	},
}

// 创建真实的API处理器 (简化版本，避免复杂依赖)
function createApiHandler() {
	try {
		const OpenAI = require("openai")

		const client = new OpenAI({
			baseURL: API_CONFIG.openai.baseUrl,
			apiKey: API_CONFIG.openai.apiKey,
		})

		return {
			async *createMessage(systemPrompt, messages) {
				const stream = await client.chat.completions.create({
					model: API_CONFIG.openai.modelId,
					messages: [
						{ role: "system", content: systemPrompt },
						...messages.map((msg) => ({
							role: msg.role,
							content: msg.content[0].text,
						})),
					],
					temperature: API_CONFIG.openai.temperature,
					max_tokens: API_CONFIG.openai.maxTokens,
					stream: true,
					stream_options: { include_usage: true },
				})

				for await (const chunk of stream) {
					const delta = chunk.choices[0]?.delta
					if (delta?.content) {
						yield {
							type: "text",
							text: delta.content,
						}
					}

					if (chunk.usage) {
						yield {
							type: "usage",
							inputTokens: chunk.usage.prompt_tokens || 0,
							outputTokens: chunk.usage.completion_tokens || 0,
						}
					}
				}
			},
		}
	} catch (error) {
		console.error("❌ 无法创建OpenAI API处理器:", error.message)
		console.log("💡 请确保设置了正确的API密钥和配置")
		console.log("💡 请安装 openai 包: npm install openai")
		return null
	}
}

function createFimHandler() {
	try {
		// 简化的FIM处理器
		return {
			async completePrompt(prompt, suffix) {
				const fetch = require("node-fetch")

				const url = `${API_CONFIG.fim.baseUrl.replace(/\/$/, "")}/completions`
				const headers = {
					"Content-Type": "application/json",
					Authorization: `Bearer ${API_CONFIG.fim.apiKey}`,
				}

				const body = {
					prompt: prompt,
					suffix: suffix,
					stream: false,
					max_tokens: API_CONFIG.fim.maxTokens,
					temperature: API_CONFIG.fim.temperature,
				}

				const response = await fetch(url, {
					method: "POST",
					headers,
					body: JSON.stringify(body),
					timeout: API_CONFIG.fim.requestTimeoutMs,
				})

				if (!response.ok) {
					throw new Error(`FIM API request failed: ${response.status} ${response.statusText}`)
				}

				const data = await response.json()
				return data.choices?.[0]?.text || ""
			},
		}
	} catch (error) {
		console.error("❌ 无法创建FIM API处理器:", error.message)
		console.log("💡 请确保设置了正确的FIM API密钥和配置")
		console.log("💡 请安装 node-fetch 包: npm install node-fetch")
		return null
	}
}

// 测试 generateCompletion (标准API模式)
async function testGenerateCompletion() {
	console.log("🔧 测试 generateCompletion (标准API模式)")
	console.log("─".repeat(80))

	// 创建真实的API处理器
	const apiHandler = createApiHandler()
	if (!apiHandler) {
		console.log("⚠️  跳过标准API测试 - API处理器创建失败")
		return
	}

	try {
		// Load required modules
		const { holeFillerTemplate } = require("./src/services/autocomplete/templating/AutocompleteTemplate")

		const document = new MockTextDocument(mockDocumentContent, "javascript", "app.js")

		for (const testCase of testCases) {
			console.log(`\n📍 ${testCase.name}`)
			console.log(`   位置: 第${testCase.position.line + 1}行, 第${testCase.position.character + 1}列`)
			console.log(`   描述: ${testCase.description}`)

			try {
				// 模拟上下文收集
				const mockCodeContext = {
					imports: ["import * as vscode from 'vscode'", "import moment from 'moment'"],
					definitions: [
					],
				}

				// 获取当前行信息
				const lineText = document.lineAt(testCase.position.line).text
				const prefix = lineText.substring(0, testCase.position.character)
				const suffix = lineText.substring(testCase.position.character)

				console.log(`   🔍 当前行: "${lineText}"`)
				console.log(`   ⬅️  前缀: "${prefix}"`)
				console.log(`   ➡️  后缀: "${suffix}"`)

				// 生成系统提示和用户提示
				const systemPrompt = holeFillerTemplate.getSystemPrompt()
				const userPrompt = await holeFillerTemplate.template(mockCodeContext, document, testCase.position, [])

				console.log(`\n   🔧 系统提示 (${systemPrompt.length} 字符):`)
				console.log("   " + "─".repeat(60))
				console.log("   " + systemPrompt.substring(0, 200) + (systemPrompt.length > 200 ? "..." : ""))

				console.log(`\n   🔧 用户提示 (${userPrompt.length} 字符):`)
				console.log("   " + "─".repeat(60))
				console.log("   " + userPrompt)

				// 调用真实的API
				console.log(`\n   📡 调用OpenAI API (${API_CONFIG.openai.modelId})...`)
				const stream = apiHandler.createMessage(systemPrompt, [
					{ role: "user", content: [{ type: "text", text: userPrompt }] },
				])

				let completion = ""
				let inputTokens = 0
				let outputTokens = 0

				for await (const chunk of stream) {
					if (chunk.type === "text") {
						completion += chunk.text
					} else if (chunk.type === "usage") {
						inputTokens = chunk.inputTokens
						outputTokens = chunk.outputTokens
					}
				}

				console.log(`\n   📊 API统计:`)
				console.log(`      输入Token: ${inputTokens}`)
				console.log(`      输出Token: ${outputTokens}`)
				console.log(`      总Token: ${inputTokens + outputTokens}`)

				console.log(`\n   🎯 生成的补全:`)
				console.log(`   "${completion}"`)
			} catch (error) {
				console.error(`   ❌ 测试失败: ${error.message}`)
			}

			console.log("\n" + "─".repeat(80))
		}
	} catch (error) {
		console.error("❌ generateCompletion 测试失败:", error)
	}
}

// 测试 generateFimCompletion (FIM模式)
async function testGenerateFimCompletion() {
	console.log("\n🔧 测试 generateFimCompletion (FIM模式)")
	console.log("─".repeat(80))

	// 创建真实的FIM API处理器
	const fimHandler = createFimHandler()
	if (!fimHandler) {
		console.log("⚠️  跳过FIM测试 - FIM API处理器创建失败")
		return
	}

	try {
		const document = new MockTextDocument(mockDocumentContent, "javascript", "app.js")

		for (const testCase of testCases) {
			console.log(`\n📍 ${testCase.name}`)
			console.log(`   位置: 第${testCase.position.line + 1}行, 第${testCase.position.character + 1}列`)
			console.log(`   描述: ${testCase.description}`)

			try {
				// 模拟FIM上下文提取逻辑
				const maxLines = 100
				const currentLine = testCase.position.line
				const linesBeforeLimit = Math.min(currentLine, Math.floor(maxLines * 0.7))
				const linesAfterLimit = Math.min(document.lineCount - currentLine - 1, maxLines - linesBeforeLimit)

				const startLine = Math.max(0, currentLine - linesBeforeLimit)
				const endLine = Math.min(document.lineCount, currentLine + linesAfterLimit + 1)

				// 提取前缀和后缀
				const beforeLines = []
				for (let i = startLine; i < currentLine; i++) {
					beforeLines.push(document.lineAt(i).text)
				}

				const currentLineText = document.lineAt(currentLine).text
				const beforeCursor = currentLineText.substring(0, testCase.position.character)
				const afterCursor = currentLineText.substring(testCase.position.character)

				const afterLines = []
				for (let i = currentLine + 1; i < endLine; i++) {
					afterLines.push(document.lineAt(i).text)
				}

				const prefix = beforeLines.join("\n") + (beforeLines.length > 0 ? "\n" : "") + beforeCursor
				const suffix = afterCursor + (afterLines.length > 0 ? "\n" : "") + afterLines.join("\n")

				console.log(`   📊 上下文统计:`)
				console.log(`      总行数: ${document.lineCount}`)
				console.log(`      提取范围: 第${startLine + 1}行 到 第${endLine}行`)
				console.log(`      前缀长度: ${prefix.length} 字符 (${beforeLines.length + (beforeCursor ? 1 : 0)} 行)`)
				console.log(`      后缀长度: ${suffix.length} 字符 (${afterLines.length + (afterCursor ? 1 : 0)} 行)`)

				console.log(`\n   🔧 FIM Prefix (前${Math.min(300, prefix.length)}字符):`)
				console.log("   " + "─".repeat(60))
				console.log("   " + prefix.substring(Math.max(0, prefix.length - 300)).replace(/\n/g, "\\n"))

				console.log(`\n   🔧 FIM Suffix (前${Math.min(300, suffix.length)}字符):`)
				console.log("   " + "─".repeat(60))
				console.log("   " + suffix.substring(0, 300).replace(/\n/g, "\\n"))

				// 调用真实的FIM API
				console.log(`\n   📡 调用FIM API (${API_CONFIG.fim.baseUrl})...`)
				const completion = await fimHandler.completePrompt(prefix, suffix)

				console.log(`\n   🎯 生成的补全:`)
				console.log(`   "${completion}"`)
			} catch (error) {
				console.error(`   ❌ 测试失败: ${error.message}`)
			}

			console.log("\n" + "─".repeat(80))
		}
	} catch (error) {
		console.error("❌ generateFimCompletion 测试失败:", error)
	}
}

// 主测试函数
async function runPromptDebugger() {
	try {
		// 显示文档信息
		const document = new MockTextDocument(mockDocumentContent, "javascript", "app.js")
		console.log("📄 测试文档信息:")
		console.log(`   文件名: ${document.fileName}`)
		console.log(`   语言: ${document.languageId}`)
		console.log(`   总行数: ${document.lineCount}`)
		console.log(`   内容长度: ${document.getText().length} 字符`)
		console.log(`   测试场景数: ${testCases.length}`)

		// 运行标准API模式测试
		await testGenerateCompletion()

		// 运行FIM模式测试 (根据用户配置决定是否运行)
		// await testGenerateFimCompletion();

		console.log("\n" + "=".repeat(80))
		console.log("✅ Autocomplete Prompt Debugger 测试完成")
		console.log("=".repeat(80))
		console.log("💡 使用说明:")
		console.log("- 📝 修改 testCases 数组来测试不同的光标位置")
		console.log("- 🔧 修改 mockDocumentContent 来测试不同的代码内容")
		console.log("- 🎯 观察生成的Prompt来优化模板和上下文提取")
		console.log("- 📊 比较两种模式的Prompt差异和效果")
	} catch (error) {
		console.error("❌ Prompt Debugger 运行失败:", error)
	}
}

// 运行测试
runPromptDebugger()

/*
🔧 配置说明:

1. API配置:
   设置环境变量或修改 API_CONFIG 对象:

   环境变量方式:
   export OPENAI_API_KEY="your-openai-api-key"
   export OPENAI_MODEL_ID="qwen3-coder-flash"
   export FIM_API_KEY="your-fim-api-key"
   export FIM_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"

   或直接修改代码中的 API_CONFIG 对象

2. 修改测试场景:
   在 testCases 数组中添加或修改测试场景:
   {
     name: '🎯 你的测试场景名称',
     position: new vscode.Position(行号, 列号), // 从0开始
     description: '场景描述'
   }

3. 修改测试文档:
   替换 mockDocumentContent 变量的内容来测试不同的代码

4. 启用/禁用测试:
   - 注释/取消注释 await testGenerateFimCompletion(); 来控制FIM测试
   - 修改 testCases 数组来选择特定的测试场景

5. 依赖包安装:
   npm install openai node-fetch

6. 调试技巧:
   - 观察系统提示是否合适
   - 检查用户提示中的上下文是否完整
   - 比较FIM模式的prefix/suffix分割是否合理
   - 验证{{FILL_HERE}}占位符的位置
   - 观察Token使用情况和API响应时间

7. 支持的API:
   - 阿里云通义千问 (qwen3-coder-flash)
   - OpenAI API (gpt-4o-mini, gpt-4等)
   - Azure OpenAI
   - DeepSeek API (FIM模式)
   - 其他OpenAI兼容API
*/
