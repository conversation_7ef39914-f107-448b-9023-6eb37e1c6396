import { QaxAuthService } from "@/qax/services/auth/QaxAuthService"
import { Empty } from "@shared/proto/cline/common"
import type { Controller } from "@/core/controller/index"
import type { EmptyRequest } from "@shared/proto/cline/common"

const qaxAuthService = QaxAuthService.getInstance()
/**
 * Handles the QAX account logout action
 * @param controller The controller instance
 * @param _request The empty request object
 * @returns Empty response
 */
export async function qaxLogoutClicked(controller: Controller, _request: EmptyRequest): Promise<Empty> {
	await controller.handleQaxSignOut()
	await qaxAuthService.handleDeauth()
	return Empty.create({})
}
