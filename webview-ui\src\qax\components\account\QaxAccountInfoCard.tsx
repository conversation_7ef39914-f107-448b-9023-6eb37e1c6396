import { useExtensionState } from "@/context/ExtensionStateContext"
import { useClineAuth } from "@/context/ClineAuthContext"
import { QaxAccountServiceClient } from "@/services/grpc-client"
import { EmptyRequest } from "@shared/proto/cline/common"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"

export const QaxAccountInfoCard = () => {
	const { navigateToAccount } = useExtensionState()
	const { qaxUser } = useClineAuth()

	let user = qaxUser

	const handleLogin = () => {
		// 直接调用 QAX 登录服务
		QaxAccountServiceClient.qaxLoginClicked(EmptyRequest.create()).catch((err: any) =>
			console.error("Failed to sign in:", err),
		)
	}

	const handleShowAccount = () => {
		navigateToAccount()
	}

	return (
		<div className="max-w-[600px]">
			{user ? (
				<VSCodeButton appearance="secondary" onClick={handleShowAccount}>
					查看账单和使用情况
				</VSCodeButton>
			) : (
				<div>
					<VSCodeButton onClick={handleLogin} className="mt-0">
						登录Qax账户
					</VSCodeButton>
				</div>
			)}
		</div>
	)
}
